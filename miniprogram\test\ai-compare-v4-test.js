/**
 * AI产品对比V4版本测试脚本
 * 对比产品：华为 Mate 70 Pro 和 苹果iPhone 16 Pro
 * 对应后端路由：POST /api/v1/products/compare-v4
 * 使用方法：node ai-compare-v4-test.js
 */

const fs = require('fs');
const path = require('path');

// 配置后端服务器地址
const BASE_URL = 'http://localhost:5000/api/v1';

/**
 * 保存数据到JSON文件
 */
function saveToJsonFile(data, filename = 'ai-compare-v4-result.json') {
  const filePath = path.join(__dirname, filename);
  const jsonData = JSON.stringify(data, null, 2);

  try {
    fs.writeFileSync(filePath, jsonData, 'utf8');
    console.log(`✅ 数据已保存到: ${filePath}`);
  } catch (error) {
    console.error('❌ 保存文件失败:', error);
  }
}

/**
 * 发送HTTP请求 (Node.js环境)
 */
function sendRequest(url, data) {
  return new Promise((resolve, reject) => {
    const https = require('https');
    const http = require('http');
    const urlObj = new URL(url);

    const postData = JSON.stringify(data);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const protocol = urlObj.protocol === 'https:' ? https : http;

    const req = protocol.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve(jsonData);
        } catch (error) {
          reject(new Error('响应数据解析失败: ' + error.message));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

/**
 * 执行AI产品对比V4测试
 */
async function testAICompareV4() {
  console.log('🧪 开始测试AI产品对比V4接口...');
  console.log('📱 测试产品: Redmi K80至尊版 vs 华为Pura 80 Ultra');

  const requestData = {
    productNames: ['Redmi  K80至尊版', '华为Pura 80 Ultra']
  };

  const apiUrl = `${BASE_URL}/products/compare-v4`;

  try {
    console.log('📡 正在发送请求...');
    const response = await sendRequest(apiUrl, requestData);

    console.log('✅ 请求成功!');
    console.log('📊 响应数据:', response);

    // 只保存后端返回的原始数据
    saveToJsonFile(response);

    console.log('🎉 测试完成，数据已保存!');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    saveToJsonFile({ error: error.message }, 'ai-compare-v4-error.json');
  }
}

/**
 * 运行测试
 */
function runTest() {
  testAICompareV4();
}

// 如果直接运行此脚本文件，自动执行测试
if (require.main === module) {
  console.log('🚀 启动AI产品对比V4测试...');
  runTest();
}

// 导出函数
module.exports = {
  testAICompareV4,
  runTest
};
