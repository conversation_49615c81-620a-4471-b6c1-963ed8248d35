<!--components/rate-score/rate-score.wxml-->

<view class="rate-score-container size-{{size}}">

  <!-- 紧凑的评分显示区域 -->
  <view class="rating-compact">
    <!-- 当前评分显示 -->
    <view class="current-rating">
      <view class="rating-score">
        <text class="score-number">{{averageRating || 0}}</text>
        <text class="score-unit">分</text>
      </view>
      <view class="rating-meta">
        <text wx:if="{{totalRatings > 0}}">{{totalRatings}}人评分</text>
        <text wx:else>暂无评分</text>
      </view>
    </view>

    <!-- 参与评分按钮 -->
    <view class="rating-action">
      <button
        wx:if="{{!showRatingPanel && !hasRated}}"
        class="participate-btn"
        bindtap="showRatingPanel"
        disabled="{{disabled}}"
      >
        参与评分
      </button>
      <view wx:elif="{{hasRated}}" class="user-rated">
        <text class="rated-text">您已评分：{{userRating}}分</text>
      </view>
      <view wx:elif="{{disabled}}" class="login-hint">
        <text class="hint-text">登录后可评分</text>
      </view>
    </view>
  </view>

  <!-- 评分面板 - 只有点击参与评分后才显示 -->
  <view wx:if="{{showRatingPanel}}" class="rating-panel">
    <view class="panel-title">{{panelTitle}}</view>

    <!-- 评分按钮 -->
    <view class="rating-buttons">
      <button
        wx:for="{{[1,2,3,4,5]}}"
        wx:key="*this"
        class="rating-btn {{userRating >= item ? 'active' : ''}}"
        data-rating="{{item}}"
        bindtap="onStarTap"
        disabled="{{submitting}}"
      >
        {{item}}星
      </button>
    </view>

    <!-- 操作按钮 -->
    <view class="panel-actions">
      <button class="cancel-btn" bindtap="hideRatingPanel" disabled="{{submitting}}">取消</button>
      <button
        class="confirm-btn"
        bindtap="confirmRating"
        disabled="{{!userRating || submitting}}"
      >
        {{submitting ? '提交中...' : '确认评分'}}
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{submitting}}" class="loading-overlay">
    <view class="loading-spinner"></view>
  </view>

</view>
