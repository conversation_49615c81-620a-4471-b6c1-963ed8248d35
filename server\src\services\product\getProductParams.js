const NewProduct = require('../../models/NewProduct');
const ProductRating = require('../../models/ProductRating');
const fs = require('fs').promises;
const path = require('path');

/**
 * 产品参数获取服务
 * 专门用于根据产品名称获取产品的详细参数信息
 */

/**
 * 根据产品名称获取产品详细参数信息
 * @param {String} productName 产品名称（精确匹配skuName）
 * @param {String} userId 用户ID（可选，用于获取用户评分）
 * @returns {Promise<Object>} 产品参数信息
 */
const getProductParamsByName = async (productName, userId = null) => {
  try {
    console.log(`🔍 正在获取产品参数信息: ${productName}`);

    // 1. 验证输入参数
    if (!productName || typeof productName !== 'string') {
      return {
        success: false,
        error: '产品名称不能为空',
        data: null
      };
    }

    // 2. 从 NewProduct 数据库中精确查找产品
    const product = await NewProduct.findOne({
      skuName: productName.trim()
    });

    if (!product) {
      return {
        success: false,
        error: `未找到产品: ${productName}`,
        data: null
      };
    }

    // 3. 获取产品评分信息
    const ratingStats = await ProductRating.getAverageRating(product._id);

    // 4. 获取用户评分信息（如果提供了用户ID）
    let userRating = null;
    if (userId && require('mongoose').Types.ObjectId.isValid(userId)) {
      try {
        userRating = await ProductRating.getUserRating(product._id, userId);
      } catch (error) {
        console.warn('获取用户评分失败:', error.message);
        // 不影响主流程，继续执行
      }
    }

    // 5. 格式化产品参数数据（包含评分信息）
    const productParams = await formatProductParams(product, ratingStats);

    // 6. 添加用户评分信息
    if (userRating) {
      productParams.rating.userRating = {
        rating: userRating.rating,
        createdAt: userRating.createdAt,
        hasRated: true
      };
    } else {
      productParams.rating.userRating = {
        hasRated: false
      };
    }

    // 7. 保存返回数据到JSON文件
    const returnData = {
      success: true,
      data: productParams
    };
    await saveProductParamsToFile(productName, returnData);

    console.log(`✅ 成功获取产品参数: ${product.skuName}`);

    return returnData;

  } catch (error) {
    console.error('❌ 获取产品参数失败:', error);
    return {
      success: false,
      error: `获取产品参数失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 格式化产品参数数据
 * @param {Object} product NewProduct 对象
 * @param {Object} ratingStats 产品评分统计信息
 * @returns {Object} 格式化后的产品参数数据
 */
const formatProductParams = (product, ratingStats = null) => {
  // 获取默认配置
  const defaultConfig = product.configurations?.find(
    config => config.name === product.defaultConfiguration
  ) || product.configurations?.[0];

  // 获取所有可用配置
  const availableConfigs = product.configurations?.filter(config => config.available) || [];
  
  // 价格信息处理 - 直接读取已存储的价格数据
  let priceInfo = {
    hasPrice: false,
    minPrice: null,
    maxPrice: null,
    defaultPrice: null,
    priceRange: null
  };

  // 优先使用已存储的 priceRange 数据
  if (product.priceRange && (product.priceRange.min || product.priceRange.max)) {
    priceInfo.hasPrice = true;
    priceInfo.minPrice = product.priceRange.min;
    priceInfo.maxPrice = product.priceRange.max;
    priceInfo.defaultPrice = defaultConfig?.price > 0 ? defaultConfig.price : priceInfo.minPrice;
    
    if (priceInfo.minPrice === priceInfo.maxPrice) {
      priceInfo.priceRange = `¥${priceInfo.minPrice}`;
    } else {
      priceInfo.priceRange = `¥${priceInfo.minPrice} - ¥${priceInfo.maxPrice}`;
    }
  } else if (product.configurations && product.configurations.length > 0) {
    // 如果没有 priceRange 数据，则从配置中计算（兜底逻辑）
    const prices = product.configurations
      .filter(config => config.price && config.price > 0)
      .map(config => config.price);
    
    if (prices.length > 0) {
      priceInfo.hasPrice = true;
      priceInfo.minPrice = Math.min(...prices);
      priceInfo.maxPrice = Math.max(...prices);
      priceInfo.defaultPrice = defaultConfig?.price > 0 ? defaultConfig.price : priceInfo.minPrice;
      
      if (priceInfo.minPrice === priceInfo.maxPrice) {
        priceInfo.priceRange = `¥${priceInfo.minPrice}`;
      } else {
        priceInfo.priceRange = `¥${priceInfo.minPrice} - ¥${priceInfo.maxPrice}`;
      }
    }
  }

  return {
    // 基本产品信息
    basic: {
      id: product._id,
      skuId: product.skuId,
      skuName: product.skuName,
      productType: product.productType,
      brandName: product.brandName,
      category: product.category,
      imageUrl: product.imageUrl,
      supportsComparison: product.supportsComparison
    },

    // 价格信息
    pricing: priceInfo,

    // 配置信息
    configurations: {
      total: product.configurations?.length || 0,
      available: availableConfigs.length,
      defaultConfiguration: product.defaultConfiguration,
      allConfigurations: product.configurations || [],
      availableConfigurations: availableConfigs,
      defaultConfigDetails: defaultConfig ? {
        name: defaultConfig.name,
        price: defaultConfig.price,
        available: defaultConfig.available,
        specs: defaultConfig.specs || {}
      } : null
    },

    // 详细规格参数
    specifications: {
      common: product.commonSpecs || {},
      varying: extractVaryingSpecs(product.configurations || [])
    },

    // 数据来源信息
    dataInfo: {
      conversionInfo: product.conversionInfo || {},
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    },

    // 产品评分信息
    rating: {
      averageRating: ratingStats ? ratingStats.averageRating : 0,
      totalRatings: ratingStats ? ratingStats.totalRatings : 0,
      ratingDistribution: ratingStats ? ratingStats.ratingDistribution : {},
      hasRatings: ratingStats ? ratingStats.totalRatings > 0 : false
    }
  };
};

/**
 * 提取配置间不同的规格参数
 * @param {Array} configurations 配置数组
 * @returns {Object} 不同配置间的变化参数
 */
const extractVaryingSpecs = (configurations) => {
  if (!configurations || configurations.length <= 1) {
    return {};
  }

  const varyingSpecs = {};
  
  // 收集所有配置的规格参数
  configurations.forEach((config, index) => {
    if (config.specs) {
      Object.keys(config.specs).forEach(category => {
        if (!varyingSpecs[category]) {
          varyingSpecs[category] = {};
        }
        
        Object.keys(config.specs[category]).forEach(param => {
          if (!varyingSpecs[category][param]) {
            varyingSpecs[category][param] = [];
          }
          
          const value = config.specs[category][param];
          const existingEntry = varyingSpecs[category][param].find(entry => entry.value === value);
          
          if (existingEntry) {
            existingEntry.configurations.push(config.name);
          } else {
            varyingSpecs[category][param].push({
              value: value,
              configurations: [config.name]
            });
          }
        });
      });
    }
  });

  // 只保留在不同配置间有变化的参数
  Object.keys(varyingSpecs).forEach(category => {
    Object.keys(varyingSpecs[category]).forEach(param => {
      if (varyingSpecs[category][param].length <= 1) {
        delete varyingSpecs[category][param];
      }
    });
    
    if (Object.keys(varyingSpecs[category]).length === 0) {
      delete varyingSpecs[category];
    }
  });

  return varyingSpecs;
};

/**
 * 保存产品参数数据到JSON文件
 * @param {String} productName 产品名称
 * @param {Object} originalData 原始返回数据（包含success字段）
 */
const saveProductParamsToFile = async (productName, originalData) => {
  try {
    // 创建保存目录
    const saveDir = path.join(__dirname, '../../exports/product_params');
    await fs.mkdir(saveDir, { recursive: true });
    
    // 生成安全的文件名（替换特殊字符）
    const safeFileName = productName
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换Windows不允许的字符
      .replace(/\s+/g, '_')          // 替换空格
      .trim();
    
    // 生成时间戳
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `${safeFileName}_${timestamp}.json`;
    const filePath = path.join(saveDir, fileName);
    
    // 直接保存原始数据，不添加任何包装
    await fs.writeFile(filePath, JSON.stringify(originalData, null, 2), 'utf8');
    
    console.log(`📁 产品参数已保存到: ${filePath}`);
    
  } catch (error) {
    console.error('❌ 保存产品参数文件失败:', error);
    // 不抛出错误，避免影响主流程
  }
};

/**
 * 转义正则表达式特殊字符
 * @param {String} str 要转义的字符串
 * @returns {String} 转义后的字符串
 */
const escapeRegex = (str) => {
  if (!str) return '';
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

/**
 * 根据产品ID获取产品详细参数信息
 * @param {String} productId 产品ID
 * @param {String} userId 用户ID（可选，用于获取用户评分）
 * @returns {Promise<Object>} 产品参数信息
 */
const getProductParamsById = async (productId, userId = null) => {
  try {
    console.log(`🔍 正在根据ID获取产品参数信息: ${productId}`);

    // 1. 验证输入参数
    if (!productId || typeof productId !== 'string') {
      return {
        success: false,
        error: '产品ID不能为空',
        data: null
      };
    }

    // 验证产品ID格式
    const mongoose = require('mongoose');
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return {
        success: false,
        error: '无效的产品ID格式',
        data: null
      };
    }

    // 2. 从 NewProduct 数据库中查找产品
    const product = await NewProduct.findById(productId);
    
    if (!product) {
      return {
        success: false,
        error: `未找到产品ID: ${productId}`,
        data: null
      };
    }

    // 3. 获取产品评分信息
    const ratingStats = await ProductRating.getAverageRating(product._id);

    // 4. 获取用户评分信息（如果提供了用户ID）
    let userRating = null;
    if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      try {
        userRating = await ProductRating.getUserRating(product._id, userId);
      } catch (error) {
        console.warn('获取用户评分失败:', error.message);
        // 不影响主流程，继续执行
      }
    }

    // 5. 格式化产品参数数据（包含评分信息）
    const productParams = await formatProductParams(product, ratingStats);

    // 6. 添加用户评分信息
    if (userRating) {
      productParams.rating.userRating = {
        rating: userRating.rating,
        createdAt: userRating.createdAt,
        hasRated: true
      };
    } else {
      productParams.rating.userRating = {
        hasRated: false
      };
    }

    // 7. 保存返回数据到JSON文件
    const returnData = {
      success: true,
      data: productParams
    };
    await saveProductParamsToFile(`${product.skuName}_${productId}`, returnData);

    console.log(`✅ 成功获取产品参数: ${product.skuName} (ID: ${productId})`);
    
    return returnData;

  } catch (error) {
    console.error('❌ 根据ID获取产品参数失败:', error);
    return {
      success: false,
      error: `获取产品参数失败: ${error.message}`,
      data: null
    };
  }
};

module.exports = {
  getProductParamsByName,
  getProductParamsById,
  formatProductParams
};
