const ProductRating = require('../../models/ProductRating');
const NewProduct = require('../../models/NewProduct');
const User = require('../../models/User');
const mongoose = require('mongoose');

/**
 * 用户对产品评分服务
 * 简化版本：每个用户只能对每个产品评分一次，不允许修改
 */

/**
 * 用户对产品进行评分
 * @param {String} userId 用户ID
 * @param {String} productId 产品ID
 * @param {Number} rating 评分 (0-5星，支持0.5增量)
 * @param {String} comment 评论（可选）
 * @returns {Promise<Object>} 评分结果
 */
const rateProduct = async (userId, productId, rating, comment = null) => {
  try {
    // 1. 验证输入参数
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return {
        success: false,
        error: '无效的用户ID',
        data: null
      };
    }
    
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return {
        success: false,
        error: '无效的产品ID',
        data: null
      };
    }
    
    // 验证评分范围和格式
    if (typeof rating !== 'number' || rating < 0 || rating > 5 || rating % 0.5 !== 0) {
      return {
        success: false,
        error: '评分必须是0-5之间的数字，支持0.5增量',
        data: null
      };
    }
    
    // 验证评论长度
    if (comment && typeof comment === 'string' && comment.trim().length > 500) {
      return {
        success: false,
        error: '评论不能超过500字符',
        data: null
      };
    }
    
    // 2. 验证用户是否存在且活跃
    const user = await User.findById(userId).select('isActive');
    if (!user) {
      return {
        success: false,
        error: '用户不存在',
        data: null
      };
    }
    
    if (!user.isActive) {
      return {
        success: false,
        error: '账户已被禁用',
        data: null
      };
    }
    
    // 3. 验证产品是否存在
    const product = await NewProduct.findById(productId)
      .select('skuName brandName category priceRange');
    
    if (!product) {
      return {
        success: false,
        error: '产品不存在',
        data: null
      };
    }
    
    // 4. 检查用户是否已经评分过（每个用户对每个产品只能评分一次）
    const existingRating = await ProductRating.findOne({
      productId: productId,
      userId: userId
    });
    
    if (existingRating) {
      return {
        success: false,
        error: '您已经对此产品评分过了，每个用户只能评分一次',
        data: null
      };
    }
    
    // 5. 获取评分时的产品价格
    let priceAtRating = null;
    if (product.priceRange && product.priceRange.min) {
      // 使用最低价格作为评分时的价格参考
      priceAtRating = product.priceRange.min;
    }
    
    // 6. 创建新评分
    const ratingRecord = new ProductRating({
      productId: productId,
      userId: userId,
      rating: rating,
      comment: comment ? comment.trim() : null,
      productName: product.skuName,
      brandName: product.brandName,
      productCategory: product.category || '电子产品',
      priceAtRating: priceAtRating
    });
    
    const savedRating = await ratingRecord.save();
    
    // 7. 获取更新后的平均评分
    const averageRatingData = await ProductRating.getAverageRating(new mongoose.Types.ObjectId(productId));
    
    console.log('获取到的产品平均评分数据:', {
      averageRating: averageRatingData.averageRating,
      totalRatings: averageRatingData.totalRatings,
      productId: productId,
      productName: product.skuName
    });
    
    return {
      success: true,
      data: {
        ratingId: savedRating._id,
        userRating: rating,
        comment: savedRating.comment,
        createdAt: savedRating.createdAt,
        message: '评分成功',
        // 更新后的评分统计信息
        updatedRatingStats: {
          averageRating: averageRatingData.averageRating,
          totalRatings: averageRatingData.totalRatings,
          ratingDistribution: averageRatingData.ratingDistribution,
          lastRatingUpdate: new Date()
        },
        // 产品信息
        productInfo: {
          id: product._id,
          name: product.skuName,
          brand: product.brandName,
          category: product.category
        }
      }
    };
    
  } catch (error) {
    console.error('用户产品评分失败:', error);
    
    // 处理唯一索引冲突错误
    if (error.code === 11000) {
      return {
        success: false,
        error: '您已经对此产品评分过了，每个用户只能评分一次',
        data: null
      };
    }
    
    return {
      success: false,
      error: `评分失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 获取产品的评分统计信息
 * @param {String} productId 产品ID
 * @returns {Promise<Object>} 评分统计信息
 */
const getProductRatingStats = async (productId) => {
  try {
    // 验证产品ID
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return {
        success: false,
        error: '无效的产品ID',
        data: null
      };
    }
    
    // 验证产品是否存在
    const product = await NewProduct.findById(productId)
      .select('skuName brandName category');
    
    if (!product) {
      return {
        success: false,
        error: '产品不存在',
        data: null
      };
    }
    
    // 获取评分统计
    const ratingStats = await ProductRating.getAverageRating(productId);
    
    return {
      success: true,
      data: {
        productInfo: {
          id: product._id,
          name: product.skuName,
          brand: product.brandName,
          category: product.category
        },
        ratingStats: ratingStats
      }
    };
    
  } catch (error) {
    console.error('获取产品评分统计失败:', error);
    return {
      success: false,
      error: `获取评分统计失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 获取用户对产品的评分
 * @param {String} userId 用户ID
 * @param {String} productId 产品ID
 * @returns {Promise<Object>} 用户评分信息
 */
const getUserProductRating = async (userId, productId) => {
  try {
    // 验证输入参数
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return {
        success: false,
        error: '无效的用户ID',
        data: null
      };
    }
    
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return {
        success: false,
        error: '无效的产品ID',
        data: null
      };
    }
    
    // 获取用户评分
    const userRating = await ProductRating.getUserRating(productId, userId);
    
    if (!userRating) {
      return {
        success: true,
        data: {
          hasRated: false,
          rating: null,
          comment: null,
          createdAt: null
        }
      };
    }
    
    return {
      success: true,
      data: {
        hasRated: true,
        rating: userRating.rating,
        comment: userRating.comment,
        createdAt: userRating.createdAt,
        priceAtRating: userRating.priceAtRating,
        isVerifiedPurchase: userRating.isVerifiedPurchase
      }
    };
    
  } catch (error) {
    console.error('获取用户产品评分失败:', error);
    return {
      success: false,
      error: `获取用户评分失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 获取产品的评分列表
 * @param {String} productId 产品ID
 * @param {Number} page 页码（从1开始）
 * @param {Number} pageSize 每页数量
 * @returns {Promise<Object>} 评分列表
 */
const getProductRatingList = async (productId, page = 1, pageSize = 10) => {
  try {
    // 验证产品ID
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return {
        success: false,
        error: '无效的产品ID',
        data: null
      };
    }
    
    // 验证分页参数
    const pageNum = Math.max(1, parseInt(page));
    const size = Math.min(50, Math.max(1, parseInt(pageSize))); // 限制每页最多50条
    const skip = (pageNum - 1) * size;
    
    // 获取评分列表
    const ratings = await ProductRating.getProductRatings(productId, size, skip);
    
    // 获取总数
    const totalCount = await ProductRating.countDocuments({ productId: productId });
    
    return {
      success: true,
      data: {
        ratings: ratings,
        pagination: {
          currentPage: pageNum,
          pageSize: size,
          totalCount: totalCount,
          totalPages: Math.ceil(totalCount / size),
          hasNext: pageNum * size < totalCount,
          hasPrev: pageNum > 1
        }
      }
    };
    
  } catch (error) {
    console.error('获取产品评分列表失败:', error);
    return {
      success: false,
      error: `获取评分列表失败: ${error.message}`,
      data: null
    };
  }
};

module.exports = {
  rateProduct,
  getProductRatingStats,
  getUserProductRating,
  getProductRatingList
};
