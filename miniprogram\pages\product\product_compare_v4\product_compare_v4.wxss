/* pages/product/product_compare_v4/product_compare_v4.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #fff;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e3e3e3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #fff;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 对比结果 */
.compare-result {
  background: #fff;
  min-height: 100vh;
}

/* 产品头部 */
.products-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 40rpx 30rpx 30rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: bold;
}

.products-scroll {
  margin-bottom: 30rpx;
}

.products-list {
  display: flex;
  gap: 20rpx;
  padding: 0 10rpx;
}

.product-card {
  flex-shrink: 0;
  width: 200rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.product-card:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 120rpx;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
}

.product-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.product-name {
  font-size: 24rpx;
  line-height: 1.4;
  text-align: center;
  color: #fff;
  word-break: break-all;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
  padding: 20rpx;
  color: #fff;
  font-size: 26rpx;
  backdrop-filter: blur(10rpx);
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.25);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.btn-text {
  font-size: 26rpx;
}

/* AI分析结果 */
.ai-analysis {
  padding: 0;
}

/* 通用section样式 */
.summary-section,
.specs-section,
.pros-cons-section,
.scenarios-section,
.purchase-advice-section,
.data-info-section {
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1rpx solid #e9ecef;
  cursor: pointer;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-toggle {
  display: flex;
  align-items: center;
}

/* 评分区域样式 */
.rating-section {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.rating-content {
  padding: 30rpx;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

/* 对比摘要 */
.summary-content {
  padding: 30rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.summary-meta {
  display: flex;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.meta-item {
  font-size: 26rpx;
  color: #666;
  background: #f8f9fa;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
}

.key-differences {
  margin-top: 30rpx;
}

.differences-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.differences-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.difference-item {
  padding: 20rpx;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-radius: 12rpx;
  border-left: 6rpx solid #e53e3e;
}

.difference-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 技术规格对比 */
.specs-content {
  padding: 0;
}

.spec-category {
  border-bottom: 1rpx solid #f0f0f0;
}

.spec-category:last-child {
  border-bottom: none;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin-bottom: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.category-header:active {
  background: #f0f0f0;
}

.category-header.expanded {
  background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 100%);
  box-shadow: 0 4rpx 12rpx rgba(25,118,210,0.2);
}

.category-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.category-toggle {
  display: flex;
  align-items: center;
}

.category-content {
  overflow: hidden;
  transition: all 0.3s ease;
}

.category-content.show {
  max-height: none;
}

.category-content.hide {
  max-height: 0;
}

.spec-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background: #fafbfc;
  margin: 2rpx 0;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #1976d2;
  margin-bottom: 20rpx;
}

.spec-values {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.value-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.product-label {
  flex-shrink: 0;
  width: 200rpx;
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
}

.product-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.spec-analysis {
  padding: 20rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border-radius: 12rpx;
  border-left: 6rpx solid #28a745;
}

.analysis-text {
  font-size: 24rpx;
  color: #155724;
  line-height: 1.5;
}

/* 优缺点分析 */
.pros-cons-content {
  padding: 0;
}

.product-analysis {
  border-bottom: 1rpx solid #f0f0f0;
}

.product-analysis:last-child {
  border-bottom: none;
}

.product-analysis-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin-bottom: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.product-analysis-header:active {
  background: #f0f0f0;
}

.product-analysis-header.expanded {
  background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 100%);
  box-shadow: 0 4rpx 12rpx rgba(25,118,210,0.2);
}

.product-analysis-header .product-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.product-toggle {
  display: flex;
  align-items: center;
}

.product-analysis-content {
  overflow: hidden;
  transition: all 0.3s ease;
  padding: 0 30rpx 30rpx;
}

.product-analysis-content.show {
  max-height: none;
}

.product-analysis-content.hide {
  max-height: 0;
  padding: 0 30rpx;
}

.pros-section,
.cons-section {
  margin-bottom: 30rpx;
}

.pros-title,
.cons-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.pros-icon,
.cons-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.pros-text,
.cons-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.pros-list,
.cons-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.pro-item {
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #28a745;
}

.con-item {
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #dc3545;
}

.pro-text,
.con-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.overall-rating {
  margin-top: 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 12rpx;
  border-left: 6rpx solid #ffc107;
}

.rating-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.rating-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 使用场景推荐 */
.scenarios-content {
  padding: 0;
}

.scenario-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.scenario-item:last-child {
  border-bottom: none;
}

.scenario-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin-bottom: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.scenario-header:active {
  background: #f0f0f0;
}

.scenario-header.expanded {
  background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 100%);
  box-shadow: 0 4rpx 12rpx rgba(25,118,210,0.2);
}

.scenario-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.scenario-toggle {
  display: flex;
  align-items: center;
}

.scenario-content {
  overflow: hidden;
  transition: all 0.3s ease;
}

.scenario-content.show {
  max-height: none;
}

.scenario-content.hide {
  max-height: 0;
}

.scenario-description {
  padding: 30rpx;
  background: #f8f9fa;
}

.description-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}

.scenario-recommendation {
  padding: 30rpx;
}

.recommended-product {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.recommend-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
}

.recommend-product {
  font-size: 26rpx;
  font-weight: bold;
  color: #007aff;
  background: #e3f2fd;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.recommend-reason {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.reason-label {
  font-size: 26rpx;
  color: #666;
  font-weight: bold;
}

.reason-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #28a745;
}

/* 购买建议 */
.purchase-advice-content {
  background: #fff;
}

.advice-subsection {
  border-bottom: 1rpx solid #f0f0f0;
}

.advice-subsection:last-child {
  border-bottom: none;
}

.advice-subsection-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin-bottom: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.advice-subsection-header:active {
  background: #f0f0f0;
}

.advice-subsection-header.expanded {
  background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 100%);
  box-shadow: 0 4rpx 12rpx rgba(25,118,210,0.2);
}

.subsection-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.subsection-toggle {
  display: flex;
  align-items: center;
}

.advice-subsection-content {
  overflow: hidden;
  transition: all 0.3s ease;
}

.advice-subsection-content.show {
  max-height: none;
}

.advice-subsection-content.hide {
  max-height: 0;
}

.advice-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin: 20rpx 30rpx;
}

.advice-item:last-child {
  margin-bottom: 20rpx;
}

.user-type {
  font-size: 26rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 12rpx;
}

.recommendation {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.note-item {
  margin: 16rpx 30rpx;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #ffc107;
}

.note-item:last-child {
  margin-bottom: 16rpx;
}

.note-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 数据信息 */
.data-info-content {
  padding: 30rpx;
  background: #f8f9fa;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  flex-shrink: 0;
  width: 160rpx;
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #fff;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .products-list {
    gap: 16rpx;
  }
  
  .product-card {
    width: 180rpx;
    padding: 16rpx;
  }
  
  .product-image-container {
    height: 100rpx;
  }
  
  .spec-values {
    gap: 12rpx;
  }
  
  .value-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
  
  .product-label {
    width: 100%;
    margin-right: 0;
  }
}