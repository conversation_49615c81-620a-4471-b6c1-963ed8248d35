const mongoose = require('mongoose');

/**
 * 用户对产品评分模型
 * 用于存储用户对具体产品的评分数据
 */
const ProductRatingSchema = new mongoose.Schema(
  {
    // 关联的产品ID
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'NewProduct',
      required: true,
      index: true
    },
    
    // 评分用户ID
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    
    // 用户评分 (0-5星)
    rating: {
      type: Number,
      required: true,
      min: [0, '评分不能低于0星'],
      max: [5, '评分不能超过5星'],
      validate: {
        validator: function(v) {
          // 允许0.5的增量，即0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5
          return v % 0.5 === 0;
        },
        message: '评分必须是0.5的倍数'
      }
    },
    
    // 评分评论（可选）
    comment: {
      type: String,
      maxlength: [500, '评论不能超过500字符'],
      trim: true
    },
    
    // 产品名称（冗余存储，便于查询和统计）
    productName: {
      type: String,
      required: true,
      index: true
    },
    
    // 产品品牌（冗余存储，便于分类统计）
    brandName: {
      type: String,
      required: true,
      index: true
    },
    
    // 产品类别（冗余存储，便于分类统计）
    productCategory: {
      type: String,
      required: true,
      index: true
    },
    
    // 评分时的产品价格（记录评分时的价格信息）
    priceAtRating: {
      type: Number,
      min: 0
    },
    
    // 是否为验证购买用户（预留字段）
    isVerifiedPurchase: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true
  }
);

// 复合索引：确保同一用户对同一产品只能评分一次
ProductRatingSchema.index(
  { productId: 1, userId: 1 }, 
  { unique: true }
);

// 索引优化
ProductRatingSchema.index({ productCategory: 1, rating: 1 }); // 按产品类别和评分查询
ProductRatingSchema.index({ brandName: 1, rating: 1 }); // 按品牌和评分查询
ProductRatingSchema.index({ rating: 1 }); // 按评分查询
ProductRatingSchema.index({ createdAt: -1 }); // 按时间排序
ProductRatingSchema.index({ isVerifiedPurchase: 1 }); // 按验证购买状态查询

// 静态方法：获取指定产品的平均评分
ProductRatingSchema.statics.getAverageRating = async function(productId) {
  // 确保productId是ObjectId类型
  const objectId = productId instanceof mongoose.Types.ObjectId ? 
    productId : new mongoose.Types.ObjectId(productId);
  
  const result = await this.aggregate([
    { $match: { productId: objectId } },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalRatings: { $sum: 1 },
        ratingDistribution: {
          $push: '$rating'
        }
      }
    }
  ]);
  
  if (result.length === 0) {
    return {
      averageRating: 0,
      totalRatings: 0,
      ratingDistribution: {}
    };
  }
  
  const data = result[0];
  
  // 计算评分分布
  const distribution = {};
  for (let i = 0; i <= 5; i += 0.5) {
    distribution[i] = 0;
  }
  
  data.ratingDistribution.forEach(rating => {
    distribution[rating] = (distribution[rating] || 0) + 1;
  });
  
  return {
    averageRating: Math.round(data.averageRating * 10) / 10, // 保留1位小数
    totalRatings: data.totalRatings,
    ratingDistribution: distribution
  };
};

// 静态方法：检查用户是否已对指定产品评分
ProductRatingSchema.statics.hasUserRated = async function(productId, userId) {
  // 确保ID是ObjectId类型
  const productObjectId = productId instanceof mongoose.Types.ObjectId ? 
    productId : new mongoose.Types.ObjectId(productId);
  const userObjectId = userId instanceof mongoose.Types.ObjectId ? 
    userId : new mongoose.Types.ObjectId(userId);
  
  const rating = await this.findOne({
    productId: productObjectId,
    userId: userObjectId
  });
  
  return rating !== null;
};

// 静态方法：获取用户对指定产品的评分
ProductRatingSchema.statics.getUserRating = async function(productId, userId) {
  // 确保ID是ObjectId类型
  const productObjectId = productId instanceof mongoose.Types.ObjectId ? 
    productId : new mongoose.Types.ObjectId(productId);
  const userObjectId = userId instanceof mongoose.Types.ObjectId ? 
    userId : new mongoose.Types.ObjectId(userId);
  
  return await this.findOne({
    productId: productObjectId,
    userId: userObjectId
  }).select('rating comment createdAt priceAtRating isVerifiedPurchase');
};

// 静态方法：获取产品的最新评分列表
ProductRatingSchema.statics.getProductRatings = async function(productId, limit = 10, skip = 0) {
  // 确保productId是ObjectId类型
  const objectId = productId instanceof mongoose.Types.ObjectId ? 
    productId : new mongoose.Types.ObjectId(productId);
  
  return await this.find({ productId: objectId })
    .populate('userId', 'nickname avatar')
    .select('rating comment createdAt priceAtRating isVerifiedPurchase')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip);
};

// 实例方法：更新评分
ProductRatingSchema.methods.updateRating = async function(newRating, newComment = null) {
  this.rating = newRating;
  if (newComment !== null) {
    this.comment = newComment;
  }
  this.updatedAt = new Date();
  return await this.save();
};

module.exports = mongoose.model('ProductRating', ProductRatingSchema);
